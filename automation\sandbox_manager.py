import time
import os
from automation.capture.windowcapture import WindowCapture
from mylogger.MyLogger import <PERSON><PERSON>ogger
from utils.config_manager import config_manager
import win32gui
from automation.controller.OCRController import OCRController
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.core.handle_manager import handle_manager

# 创建模块级别的日志实例
logger = MyLogger("SandboxManager", save_log=True)

class WindowsNotFoundException(Exception):
    def __init__(self, windows_name):
        super(WindowsNotFoundException, self).__init__(f"没有找到名称为'{windows_name}'的窗口!")

class SandboxManager:
    """沙盒管理器，负责通过SandMan.exe自动化创建和管理沙盒"""

    # properties
    w = 1920
    h = 1080
    hwnd = None
    cropped_x = 0
    cropped_y = 0
    offset_x = 0
    offset_y = 0
    window_name="Sandboxie-Plus"
    sandboxie_capture = None
    ocr_controller = None
    ocr_results = []
    
    def __init__(self):
        self.settings = config_manager.get("settings")
        self.sandman_process = None
        
    def create_sandbox(self, sandbox_name):
        """创建新沙盒"""
        try:

            # 3. 点击"沙盒"菜单
            logger.info("点击'沙盒'菜单...")
            self.ocr_controller.bg_click(35,6)
            time.sleep(1)
            
            # 4. 点击"创建新沙盒"
            logger.info("点击'创建新建沙箱'...")
            self.ocr_controller.bg_click(35,40)
            time.sleep(1)


            _sandboxie_capture = WindowCapture("新建沙箱向导")
            _sandboxie_capture._update_rect()
            _ocr_controller = OCRController(gc=_sandboxie_capture)
            # 5. 输入沙盒名称
            logger.info(f"输入沙盒名称: {sandbox_name}")
            # 后台输入沙盒名称
            _ocr_controller.bg_type_string(sandbox_name)
            time.sleep(0.5)


            # 6. 点击标准沙箱
            logger.info(f"点击：标准沙箱")
            # 在"新建沙箱"文本中心偏移位置点击
            if not _ocr_controller.find_text_and_bg_click("标准沙箱",offset_x=-40):
                
                logger.error("未找到'新建沙箱'选项")
            time.sleep(1)

            # 7. 点击"确定"按钮
            logger.info("点击'完成'按钮...")
            if not _ocr_controller.find_text_and_bg_click("完成"):
                return False
            
            time.sleep(2)  # 等待创建完成
            logger.info(f"沙盒 {sandbox_name} 创建成功")
            return True
            
        except Exception as e:
            logger.error(f"创建沙盒失败: {e}")
            return False

    def initialize_sandboxes(self, max_count=9):
        """初始化所有沙盒（Elsrift_1 到 Elsrift_max_count）

        检测流程：
        1. 检测_start_sandman是否启动，如果没有就启动
        2. OCR检测主窗口图片有没有Elsrift_1到Elsrift_max_count文字
        3. 创建没有的沙盒
        """
        try:
            logger.info("开始批量初始化沙盒...")

            # 2. 获取Sandboxie实例进行OCR检测
            self.sandboxie_capture, self.ocr_controller = handle_manager.get_sandboxie_instances()

            # 3. OCR检测主窗口中已存在的沙盒
            logger.info("正在检测主窗口中已存在的沙盒...")
            existing_sandboxes = set()

            # 获取OCR结果
            self.ocr_results = self.ocr_controller.get_ocr_result(mss_mode=False)

            # 检查每个可能的沙盒名称
            for i in range(1, max_count + 1):
                sandbox_name = f"Elsrift{i}"
                # 在OCR结果中查找沙盒名称
                for ocr_result in self.ocr_results:
                    if sandbox_name in ocr_result.text:
                        existing_sandboxes.add(sandbox_name)
                        logger.info(f"检测到已存在的沙盒: {sandbox_name}")
                        break

            logger.info(f"已存在的沙盒: {existing_sandboxes}")

            # 4. 创建不存在的沙盒
            success_count = len(existing_sandboxes)  # 已存在的沙盒算作成功
            missing_sandboxes = []

            for i in range(1, max_count + 1):
                sandbox_name = f"Elsrift{i}"
                if sandbox_name not in existing_sandboxes:
                    missing_sandboxes.append(sandbox_name)

            if missing_sandboxes:
                logger.info(f"需要创建的沙盒: {missing_sandboxes}")

                for sandbox_name in missing_sandboxes:
                    logger.info(f"正在创建沙盒: {sandbox_name}")

                    if self.create_sandbox(sandbox_name):
                        success_count += 1
                        logger.info(f"沙盒 {sandbox_name} 创建成功")
                    else:
                        logger.error(f"沙盒 {sandbox_name} 创建失败")
            else:
                logger.info("所有沙盒都已存在，无需创建")

            logger.info(f"沙盒初始化完成，成功: {success_count}/{max_count}")
            
            # 5. 初始化完成后使用HandleManager的cleanup方法关闭Sandboxie
            logger.info("沙盒初始化完成，正在关闭Sandboxie...")
            handle_manager.cleanup()
            
            return success_count == max_count

        except Exception as e:
            logger.error(f"批量初始化沙盒失败: {e}")
            # 即使出错也要尝试清理
            try:
                handle_manager.cleanup()
            except:
                pass
            return False
   







