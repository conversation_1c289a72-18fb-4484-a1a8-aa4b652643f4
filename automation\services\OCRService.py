import time
import os
from paddleocr import PaddleOCR
import cv2
from mylogger.MyLogger import <PERSON><PERSON>ogger
from automation.myutils.configutils import resource_path
import logging

logging.getLogger('ppocr').setLevel(logging.WARNING)
logger = MyLogger('ocr_service')

# OCR模型路径配置
det_dir = os.path.join(resource_path, 'ocr', 'PP-OCRv5_mobile_det_infer')
rec_dir = os.path.join(resource_path, 'ocr', 'PP-OCRv5_mobile_rec_infer')
ori_dir = os.path.join(resource_path, 'ocr', 'PP-LCNet_x0_25_textline_ori_infer')

ocr = PaddleOCR(
    lang="ch",
    # 核心目录参数
    text_detection_model_name="PP-OCRv5_mobile_det",
    text_recognition_model_name="PP-OCRv5_mobile_rec",
    textline_orientation_model_name="PP-LCNet_x0_25_textline_ori",
    text_detection_model_dir=det_dir,
    text_recognition_model_dir=rec_dir,
    textline_orientation_model_dir=ori_dir,
    use_textline_orientation=False,
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
)

class OCRService:
    def __init__(self):
        # OCRService只负责纯OCR识别，不依赖任何capture
        pass
    
    def recognize_text(self, image):
        """纯OCR识别，输入图像返回结果"""
                    # 转换图像格式为BGR 3通道
        if len(image.shape) == 3:
            if image.shape[2] == 4:  # BGRA -> BGR
                image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
            elif image.shape[2] == 1:  # 灰度 -> BGR
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        elif len(image.shape) == 2:  # 灰度 -> BGR
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

        # cv2.imshow('Window Screenshot', image)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()

        # 保存图片到根目录
        # timestamp = int(time.time() * 1000)  # 毫秒时间戳
        # filename = f"ocr_image_{timestamp}.png"
        # cv2.imwrite(filename, image)
        # logger.info(f"OCR图片已保存: {filename}")
        
        result = ocr.predict(image)
        return result
    
    def recognize_region(self, image, x, y, width, height):
        """识别图像指定区域"""
        region = image[y:y+height, x:x+width]
        return self.recognize_text(region)
    
    @staticmethod
    def get_text_from_result(ocr_result):
        """从OCR结果中提取纯文本"""
        if not ocr_result:
            return ""
        
        texts = []
        for line in ocr_result:
            if line:
                for item in line:
                    if len(item) >= 2:
                        texts.append(item[1][0])
        
        return " ".join(texts)





