"""
句柄管理器 - 专门处理窗口句柄失效问题
"""
import time
import psutil
import win32gui
import subprocess
import os
from mylogger.MyLogger import MyLogger
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController

logger = MyLogger('HandleManager', save_log=True)

class HandleManager:
    """句柄管理器 - 单例模式管理窗口句柄"""
    
    _instance = None
    _sandboxie_capture = None
    _ocr_controller = None
    _sandman_process = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(HandleManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            logger.info("HandleManager 初始化")
    
    def is_handle_valid(self, capture):
        """检查窗口句柄是否有效"""
        if not capture or not capture.hwnd:
            return False
        
        try:
            hwnd = capture.hwnd
            
            # 检查句柄是否仍然是有效窗口
            if not win32gui.IsWindow(hwnd):
                return False
            
            # 检查窗口是否可见
            if not win32gui.IsWindowVisible(hwnd):
                return False
            
            # 检查窗口标题
            title = win32gui.GetWindowText(hwnd)
            if "sandboxie" not in title.lower():
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"句柄验证失败: {e}")
            return False
    
    def refresh_handle(self, capture):
        """刷新窗口句柄"""
        try:
            old_hwnd = capture.hwnd
            logger.info(f"刷新句柄，当前句柄: {old_hwnd}")
            
            # 调用_update_rect来重新获取窗口句柄
            capture._update_rect()
            
            new_hwnd = capture.hwnd
            if new_hwnd:
                if new_hwnd != old_hwnd:
                    logger.info(f"句柄已更新: {old_hwnd} -> {new_hwnd}")
                else:
                    logger.info(f"句柄刷新完成，句柄未变: {new_hwnd}")
                return True
            else:
                logger.error("句柄刷新失败，未找到有效窗口")
                return False
                
        except Exception as e:
            logger.error(f"刷新句柄异常: {e}")
            return False
        
    def _is_sandman_running(self):
        """检测SandMan.exe进程是否正在运行 - 使用模糊查询"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name']:
                    proc_name = proc.info['name'].lower()
                    # 模糊匹配：包含sandman或sandboxie相关的进程名
                    if any(keyword in proc_name for keyword in ['sandman', 'sandboxie']):
                        logger.debug(f"发现Sandboxie相关进程: {proc.info['name']} PID={proc.info['pid']}")
                        return True
            return False
        except Exception as e:
            logger.error(f"检测SandMan.exe进程失败: {e}")
            return False
    
    def get_sandboxie_instances(self):
        """
        获取Sandboxie实例，自动处理句柄失效问题
        返回: (capture, ocr_controller) 或 False
        """
        # 1. 检测SandMan.exe是否已经启动，如果没有就启动
        if not self._is_sandman_running():
            logger.info("SandMan.exe未运行，正在启动...")
            if not self._start_sandman():
                logger.error("启动SandMan.exe失败")
                return False, None  # 返回元组而不是False
            time.sleep(3)  # 等待启动完成
        else:
            logger.info("SandMan.exe已在运行")

        # 第一次创建实例
        if self._sandboxie_capture is None:
            logger.info("首次创建Sandboxie实例")
            try:
                self._sandboxie_capture = IdentifySandboxieCapture()
                self._ocr_controller = OCRController(gc=self._sandboxie_capture)
                logger.info(f"实例创建完成，初始句柄: {self._sandboxie_capture.hwnd}")
                return self._sandboxie_capture, self._ocr_controller
            except Exception as e:
                logger.error(f"创建Sandboxie实例失败: {e}")
                return False, None
        
        # 检查现有句柄是否有效
        if self.is_handle_valid(self._sandboxie_capture):
            logger.debug("现有句柄仍然有效")
            return self._sandboxie_capture, self._ocr_controller
        
        # 句柄无效，尝试刷新
        logger.warning("检测到句柄失效，尝试刷新...")
        if self.refresh_handle(self._sandboxie_capture):
            logger.info("句柄刷新成功")
            # 确保OCR控制器引用的是同一个capture对象
            self._ocr_controller.gc = self._sandboxie_capture
            return self._sandboxie_capture, self._ocr_controller
        else:
            logger.error("句柄刷新失败，重新创建实例")
            # 只有在刷新失败时才重新创建
            try:
                self._sandboxie_capture = IdentifySandboxieCapture()
                self._ocr_controller = OCRController(gc=self._sandboxie_capture)
                return self._sandboxie_capture, self._ocr_controller
            except Exception as e:
                logger.error(f"重新创建实例失败: {e}")
                # 最后的尝试：启动Sandboxie后再试
                if self._start_sandman():
                    time.sleep(3)
                    try:
                        self._sandboxie_capture = IdentifySandboxieCapture()
                        self._ocr_controller = OCRController(gc=self._sandboxie_capture)
                        return self._sandboxie_capture, self._ocr_controller
                    except Exception as e2:
                        logger.error(f"最终创建实例失败: {e2}")
                        return False, None
                else:
                    logger.error("无法启动SandMan.exe")
                    return False, None
    
    def reset_instances(self):
        """重置实例（当你确定窗口已关闭时可以调用）"""
        logger.info("重置Sandboxie实例")
        self._sandboxie_capture = None
        self._ocr_controller = None
    
    def list_sandboxie_windows(self):
        """列出所有Sandboxie相关窗口"""
        def enum_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    title = win32gui.GetWindowText(hwnd)
                    if title and ("sandboxie" in title.lower() or "sandbox" in title.lower()):
                        windows.append({
                            'hwnd': hwnd,
                            'title': title,
                            'is_visible': win32gui.IsWindowVisible(hwnd),
                            'is_window': win32gui.IsWindow(hwnd)
                        })
                except:
                    pass
            return True

        windows = []
        win32gui.EnumWindows(enum_callback, windows)
        return windows
    
    def get_current_handle_info(self):
        """获取当前句柄信息（用于调试）"""
        # 先列出所有Sandboxie窗口
        sandboxie_windows = self.list_sandboxie_windows()
        logger.info(f"找到 {len(sandboxie_windows)} 个Sandboxie相关窗口:")
        for i, win in enumerate(sandboxie_windows):
            logger.info(f"  {i+1}. 句柄:{win['hwnd']}, 标题:'{win['title']}'")

        if self._sandboxie_capture:
            try:
                hwnd = self._sandboxie_capture.hwnd
                if hwnd:
                    title = win32gui.GetWindowText(hwnd)
                    is_valid = win32gui.IsWindow(hwnd)
                    is_visible = win32gui.IsWindowVisible(hwnd)

                    return {
                        'hwnd': hwnd,
                        'title': title,
                        'is_valid': is_valid,
                        'is_visible': is_visible,
                        'handle_check': self.is_handle_valid(self._sandboxie_capture),
                        'available_windows': sandboxie_windows
                    }
            except Exception as e:
                return {'error': str(e), 'available_windows': sandboxie_windows}
        return {'status': 'no_instance', 'available_windows': sandboxie_windows}
    
    def _get_sandman_path(self):
        """获取SandMan.exe路径"""
        from utils.config_manager import config_manager
        settings = config_manager.get("settings")
        sandboxie_path = settings.get('sandboxie_executable_path')
        if sandboxie_path:
            sandman_path = sandboxie_path.replace('Start.exe', 'SandMan.exe')
            if os.path.exists(sandman_path):
                return sandman_path
        return None

    def _start_sandman(self):
        """启动SandMan.exe"""
        try:
            sandman_path = self._get_sandman_path()
            if not sandman_path:
                logger.error("未找到SandMan.exe")
                return False
                
            self._sandman_process = subprocess.Popen([sandman_path])
            time.sleep(3)  # 等待应用启动
            logger.info("SandMan.exe已启动")
            return True
        except Exception as e:
            logger.error(f"启动SandMan.exe失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self._sandman_process:
            try:
                self._sandman_process.terminate()
                logger.info("SandMan.exe进程已终止")
            except Exception as e:
                logger.error(f"终止SandMan.exe进程失败: {e}")
        
        self.reset_instances()


# 全局单例实例
handle_manager = HandleManager()

