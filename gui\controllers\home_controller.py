from PySide6.QtCore import QObject, Qt
from PySide6.QtWidgets import QLabel, QComboBox, QWidget, QGridLayout, QPushButton
from PySide6.QtGui import QFont, QCursor
from services.account_service import AccountService
from utils.config_manager import config_manager
from datetime import datetime
from automation.game_launcher import GameLauncher
from automation.sandbox_manager import SandboxManager
from gui.components.toast_manager import show_toast
from mylogger.MyLogger import MyLogger

# 创建模块级别的日志实例
logger = MyLogger("HomeController", save_log=True)

class HomeController(QObject):
    def __init__(self, page, ui):
        super().__init__()
        self.page = page
        self.ui = ui
        self.account_widgets = []
        self.account_service = AccountService()
        self.game_launcher = GameLauncher()
        self.sandbox_manager = SandboxManager()  # 添加沙盒管理器
        
        # 连接启动按钮
        self.ui.btn_run.clicked.connect(self.start_multi_launch)
        
        # 连接初始化沙盒按钮
        self.ui.btn_initialize_sandbox.clicked.connect(self.initialize_sandboxes)
        
        self.setup_count_controls()
        self.setup_account_selection_area()
        
        # 加载多开配置并恢复状态
        selected_accounts = self.load_multi_launch_config()
        
        # 根据配置创建账号选择控件
        initial_count = self.get_current_count()
        self.update_account_selection(initial_count)
        
        # 恢复账号选择
        self.restore_account_selections(selected_accounts)

    def setup_count_controls(self):
        """设置数量控制相关功能"""
        self.ui.lineEdit_count.setText("1")
        
        # 连接按钮事件
        self.ui.btn_add.clicked.connect(self.decrease_count)
        self.ui.btn_minus.clicked.connect(self.increase_count)
        self.ui.lineEdit_count.textChanged.connect(self.on_count_changed)

    def setup_account_selection_area(self):
        """设置账号选择区域"""
        self.account_grid_layout = QGridLayout()
        self.account_grid_layout.setSpacing(10)
        self.account_grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        parent_layout = self.ui.home_account_1.parent().layout()
        if parent_layout:
            index = parent_layout.indexOf(self.ui.home_account_1)
            if index >= 0:
                self.account_container = QWidget()
                self.account_container.setStyleSheet("background-color: transparent;")
                self.account_container.setLayout(self.account_grid_layout)
                parent_layout.insertWidget(index + 1, self.account_container)

    def get_account_options(self):
        """获取账号选项列表"""
        accounts_data = self.account_service.load_accounts()
        status_data = self.account_service.load_status()
        accounts = accounts_data.get("accounts", [])
        
        options = []
        for account in accounts:
            account_id = str(account.get('id'))
            # 只显示启用的账号
            if status_data.get(account_id, {}).get("enabled", True):
                username = account.get('username', '')
                role = account.get('role', '')
                if username and role:
                    options.append(f"{username}({role})")
                elif username:
                    options.append(username)
        
        # 如果没有启用的账号，添加默认选项
        if not options:
            options.append("请先添加账号")
        
        return options

    def get_enabled_account_count(self):
        """获取启用账号的数量"""
        accounts_data = self.account_service.load_accounts()
        status_data = self.account_service.load_status()
        accounts = accounts_data.get("accounts", [])
        
        enabled_count = 0
        for account in accounts:
            account_id = str(account.get('id'))
            if status_data.get(account_id, {}).get("enabled", True):
                enabled_count += 1
        
        return enabled_count

    def create_account_selection_widget(self, account_num):
        """创建单个账号选择控件组"""
        container = QWidget()
        container.setFixedHeight(50)
        container.setMinimumWidth(350)
        
        # 创建标签
        label = QLabel(f"账号{account_num}：", container)
        label.setObjectName(f"label_acc_{account_num}")
        label.setGeometry(20, 10, 61, 31)
        
        font = QFont("Microsoft YaHei", 13)
        label.setFont(font)
        label.setStyleSheet(f"#label_acc_{account_num} {{ background-color: transparent; color: white; }}")
        
        # 创建下拉框
        combobox = QComboBox(container)
        combobox.setObjectName(f"comboBox_{account_num}")
        combobox.setGeometry(90, 10, 221, 31)  # 缩小宽度为删除按钮留空间
        
        # 添加账号选项（排除已选择的）
        available_options = self.get_available_options_for_combobox(account_num)
        combobox.addItems(available_options)
        
        # 连接选择变化事件
        combobox.currentTextChanged.connect(lambda: self.on_account_selection_changed())
        
        combobox.setStyleSheet(f"""
            #comboBox_{account_num} {{
                background-color: #282C36; 
                padding-left: 10px;
            }}
            #comboBox_{account_num} QAbstractItemView {{
                background-color: #202429;
                border: 1px solid #292C33;
                border-radius: 4px;
                padding-left: 10px;
            }}
            #comboBox_{account_num} QAbstractItemView::item {{
                height: 30px;
                margin-bottom: 2px;
            }}
        """)
        
        # 创建删除按钮
        btn_delete = QPushButton(container)
        btn_delete.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        btn_delete.setObjectName(f"btn_delete_acc_{account_num}")
        btn_delete.setGeometry(320, 12, 30, 28)
        btn_delete.setText("×")
        btn_delete.setStyleSheet(f"""
            #btn_delete_acc_{account_num} {{
                background-color: #D32F2F;
                border-radius: 10px;
                font-size: 16px;
                color: white;
                font-weight: bold;
            }}
            #btn_delete_acc_{account_num}:hover {{
                background-color: #EF5350;
            }}
        """)
        btn_delete.clicked.connect(lambda: self.delete_account_selection(account_num))
        
        return container

    def get_available_options_for_combobox(self, current_account_num):
        """获取指定下拉框可用的选项（排除其他下拉框已选择的）"""
        all_options = self.get_account_options()
        
        if not all_options or all_options == ["请先添加账号"]:
            return all_options
        
        # 获取其他下拉框已选择的选项
        selected_options = set()
        for widget in self.account_widgets:
            combobox = widget.findChild(QComboBox)
            if combobox and combobox.objectName() != f"comboBox_{current_account_num}":
                current_text = combobox.currentText()
                if current_text and current_text != "请先添加账号":
                    selected_options.add(current_text)
        
        # 返回未被选择的选项
        available_options = [opt for opt in all_options if opt not in selected_options]
        
        # 如果没有可用选项，返回提示
        if not available_options:
            available_options = ["无可用账号"]
        
        return available_options

    def on_account_selection_changed(self):
        """当账号选择发生变化时，更新其他下拉框的选项"""
        # 更新其他下拉框选项
        for i, widget in enumerate(self.account_widgets):
            combobox = widget.findChild(QComboBox)
            if combobox:
                current_selection = combobox.currentText()
                available_options = self.get_available_options_for_combobox(i + 1)
                
                combobox.blockSignals(True)
                combobox.clear()
                combobox.addItems(available_options)
                
                if current_selection in available_options:
                    combobox.setCurrentText(current_selection)
                
                combobox.blockSignals(False)
    
    # 移除保存配置

    def update_account_selection(self, count):
        """根据数量更新账号选择控件"""
        # 保存当前所有下拉框的选择状态
        current_selections = {}
        for i, widget in enumerate(self.account_widgets):
            combobox = widget.findChild(QComboBox)
            if combobox:
                current_selections[i + 1] = combobox.currentText()
    
        self.clear_account_widgets()
    
        for i in range(count):
            widget = self.create_account_selection_widget(i + 1)
            row, col = divmod(i, 2)
            self.account_grid_layout.addWidget(widget, row, col)
            self.account_widgets.append(widget)
        
            # 恢复之前的选择（如果仍然可用）
            combobox = widget.findChild(QComboBox)
            if combobox and (i + 1) in current_selections:
                previous_selection = current_selections[i + 1]
                if previous_selection in [combobox.itemText(j) for j in range(combobox.count())]:
                    combobox.setCurrentText(previous_selection)
    
        # 创建完所有控件后，更新所有下拉框的选项
        self.refresh_all_combobox_options()

    def refresh_all_combobox_options(self):
        """刷新所有下拉框的选项"""
        for i, widget in enumerate(self.account_widgets):
            combobox = widget.findChild(QComboBox)
            if combobox:
                current_selection = combobox.currentText()
                available_options = self.get_available_options_for_combobox(i + 1)
                
                combobox.blockSignals(True)
                combobox.clear()
                combobox.addItems(available_options)
                
                # 恢复选择（如果仍然可用）
                if current_selection in available_options:
                    combobox.setCurrentText(current_selection)
                
                combobox.blockSignals(False)

    def clear_account_widgets(self):
        """清除所有动态创建的账号选择控件"""
        for widget in self.account_widgets:
            widget.deleteLater()
        self.account_widgets.clear()
        
        while self.account_grid_layout.count():
            item = self.account_grid_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    def on_count_changed(self, text):
        """当数量改变时的处理"""
        self.validate_count_input(text)
        self.update_account_selection(self.get_current_count())
        # 保存配置
        self.save_multi_launch_config()

    def get_current_count(self):
        """获取当前数量值（不能超过启用账号数量）"""
        text = self.ui.lineEdit_count.text().strip()
        max_enabled = self.get_enabled_account_count()
        
        try:
            value = int(text) if text else 1
            return max(1, min(max_enabled, value))
        except ValueError:
            return 1

    def set_count(self, value):
        """设置数量值"""
        self.ui.lineEdit_count.setText(str(max(1, min(99, value))))

    def increase_count(self):
        """增加数量"""
        current = self.get_current_count()
        max_enabled = self.get_enabled_account_count()
        if current < max_enabled:
            self.set_count(current + 1)
            # 保存配置
            self.save_multi_launch_config()

    def decrease_count(self):
        """减少数量"""
        current = self.get_current_count()
        if current > 1:
            self.set_count(current - 1)
            # 保存配置
            self.save_multi_launch_config()

    def validate_count_input(self, text):
        """验证输入内容（限制不超过启用账号数量）"""
        max_enabled = self.get_enabled_account_count()
        
        if not text:
            self.ui.lineEdit_count.setText("1")
            return
        
        cleaned = ''.join(c for c in text if c.isdigit()).lstrip('0') or '1'
        
        if len(cleaned) > 2 or int(cleaned) > max_enabled:
            cleaned = str(max_enabled)
        
        if cleaned != text:
            cursor_pos = self.ui.lineEdit_count.cursorPosition()
            self.ui.lineEdit_count.setText(cleaned)
            self.ui.lineEdit_count.setCursorPosition(min(cursor_pos, len(cleaned)))

    def delete_account_selection(self, account_num):
        """删除指定的账号选择控件"""
        current_count = self.get_current_count()
        if current_count > 1:
            new_count = current_count - 1
            self.set_count(new_count)
            self.update_account_selection(new_count)
            # 保存配置
            self.save_multi_launch_config()

    def load_multi_launch_config(self):
        """加载多开配置"""
        try:
            multi_config = config_manager.get("multi_launch", default={})
            launch_config = multi_config.get("multi_launch", {})
            
            # 恢复数量
            count = launch_config.get("count", 1)
            self.set_count(count)
            
            # 恢复选择
            selected_accounts = launch_config.get("selected_accounts", [])
            return selected_accounts
        except Exception as e:
            logger.error(f"加载多开配置失败: {e}")
            return []

    def save_multi_launch_config(self):
        """保存多开配置"""
        try:
            # 收集当前选择
            selected_accounts = []
            for widget in self.account_widgets:
                combobox = widget.findChild(QComboBox)
                if combobox and combobox.currentText() not in ["请先添加账号", "无可用账号"]:
                    selected_accounts.append(combobox.currentText())
            
            config_data = {
                "multi_launch": {
                    "count": self.get_current_count(),
                    "selected_accounts": selected_accounts,
                    "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "settings": {
                    "auto_restore_selection": True,
                    "max_concurrent": 5
                }
            }
            
            config_manager.update_and_save("multi_launch", config_data)
            logger.debug(f"多开配置已保存: {len(selected_accounts)} 个账号")
            
        except Exception as e:
            logger.error(f"保存多开配置失败: {e}")

    def restore_account_selections(self, selected_accounts):
        """恢复账号选择状态"""
        try:
            for i, selected_account in enumerate(selected_accounts):
                if i < len(self.account_widgets):
                    widget = self.account_widgets[i]
                    combobox = widget.findChild(QComboBox)
                    if combobox:
                        # 检查选项是否存在
                        items = [combobox.itemText(j) for j in range(combobox.count())]
                        if selected_account in items:
                            combobox.setCurrentText(selected_account)
    
            # 恢复后刷新所有下拉框选项以确保互斥性
            self.refresh_all_combobox_options()
            logger.debug(f"已恢复 {len(selected_accounts)} 个账号选择")
    
        except Exception as e:
            logger.error(f"恢复账号选择失败: {e}")

    def start_multi_launch(self):
        """开始多开启动"""
        try:
            # 获取当前选择的账号
            selected_accounts = []
            for widget in self.account_widgets:
                combobox = widget.findChild(QComboBox)
                if combobox and combobox.currentText() not in ["请先添加账号", "无可用账号"]:
                    selected_accounts.append(combobox.currentText())
            
            if not selected_accounts:
                logger.warning("未选择任何账号，无法启动多开")
                show_toast("请先选择要启动的账号", "warning")
                return
            
            logger.info(f"开始多开启动，选择的账号: {selected_accounts}")
            
            # 启动游戏
            success_count = self.game_launcher.launch_multiple_accounts(selected_accounts)
            
            # 显示结果
            if success_count > 0:
                logger.info(f"成功启动 {success_count} 个账号")
                show_toast(f"成功启动 {success_count} 个账号", "success")
            else:
                logger.error("所有账号启动失败")
                show_toast("启动失败，请查看日志", "error")
                
        except Exception as e:
            logger.error(f"多开启动失败: {e}")
            show_toast(f"启动失败: {e}", "error")

    def initialize_sandboxes(self):
        """初始化所有沙盒"""
        try:
            # 禁用按钮防止重复点击
            self.ui.btn_initialize_sandbox.setEnabled(False)
            self.ui.btn_initialize_sandbox.setText("初始化中...")
            
            show_toast("开始初始化沙盒...", "info")
            
            # 在后台线程中执行初始化
            from PySide6.QtCore import QThread, QObject, Signal
            
            class InitWorker(QObject):
                finished = Signal(bool)
                progress = Signal(str)
                error = Signal(str)
                
                def __init__(self, sandbox_manager):
                    super().__init__()
                    self.sandbox_manager = sandbox_manager
                
                def run(self):
                    try:
                        self.progress.emit("开始初始化沙盒...")
                        success = self.sandbox_manager.initialize_sandboxes(9)
                        self.finished.emit(success)
                    except Exception as e:
                        self.error.emit(f"初始化线程异常: {e}")
                        self.finished.emit(False)
            
            self.init_thread = QThread()
            self.init_worker = InitWorker(self.sandbox_manager)
            self.init_worker.moveToThread(self.init_thread)
            
            # 连接信号
            self.init_thread.started.connect(self.init_worker.run)
            self.init_worker.finished.connect(self.on_initialize_finished)
            self.init_worker.finished.connect(self.init_thread.quit)
            self.init_worker.finished.connect(self.init_worker.deleteLater)
            self.init_thread.finished.connect(self.init_thread.deleteLater)
            
            # 启动线程
            self.init_thread.start()
            
        except Exception as e:
            show_toast(f"启动初始化失败: {e}", "error")
            self.on_initialize_finished(False)
    
    def on_initialize_finished(self, success):
        """初始化完成回调"""
        try:
            # 恢复按钮状态
            self.ui.btn_initialize_sandbox.setEnabled(True)
            self.ui.btn_initialize_sandbox.setText("初始化")
            
            if success:
                show_toast("沙盒初始化完成！", "success")
                logger.info("所有沙盒初始化成功")
            else:
                show_toast("沙盒初始化失败，请查看日志", "error")
                logger.error("沙盒初始化失败")
                
        except Exception as e:
            logger.error(f"初始化完成回调异常: {e}")





